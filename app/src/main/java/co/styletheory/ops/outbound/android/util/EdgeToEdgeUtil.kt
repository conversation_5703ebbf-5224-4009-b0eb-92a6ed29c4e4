package co.styletheory.ops.outbound.android.util

import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewGroup
import android.widget.ScrollView
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import co.styletheory.ops.outbound.android.R
import timber.log.Timber

/**
 * Utility for handling window insets and edge-to-edge display.
 * Provides consistent edge-to-edge behavior across different Android versions.
 */
object EdgeToEdgeUtil {

    private val osUtil = OSUtil()
    private const val DEFAULT_ADJUSTMENT_FACTOR = 0.8f
    private const val PHOTO_PICKER_DELAY_MS = 300L

    /**
     * Sets up edge-to-edge display for an activity with status bar color based on toolbar color.
     *
     * @param activity The activity to configure
     * @param toolbarColor Optional toolbar color to determine status bar appearance
     */
    fun setupEdgeToEdge(activity: Activity, toolbarColor: Int? = null) {
        // Enable drawing behind system bars
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)

        // Configure system bars appearance using WindowInsetsControllerCompat
        val windowInsetsController = WindowInsetsControllerCompat(activity.window, activity.window.decorView)

        // Determine if toolbar is light and set status bar color accordingly
        val isLightToolbar = if (toolbarColor != null) {
            isColorLight(toolbarColor)
        } else {
            false // Default to dark toolbar
        }

        // Set status bar color based on toolbar color
        val statusBarColor = if (isLightToolbar) {
            Color.WHITE // Light toolbar gets white status bar
        } else {
            ContextCompat.getColor(activity, R.color.color_primary) // Dark toolbar gets color_primary
        }

        // Set system bar colors
        activity.window.statusBarColor = statusBarColor
        activity.window.navigationBarColor = Color.TRANSPARENT

        // Set status bar icons color (dark icons for light background, light icons for dark background)
        windowInsetsController.isAppearanceLightStatusBars = isLightToolbar
        windowInsetsController.isAppearanceLightNavigationBars = false // Always use white icons for navigation

        // Special handling for Android 15+
        if(OSUtil().isAndroid15OrHigher()) {
            windowInsetsController.systemBarsBehavior =
                WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
    }

    /**
     * Determines if a color is light or dark.
     *
     * @param color The color to check
     * @return true if the color is light (should use dark icons), false otherwise
     */
    private fun isColorLight(color: Int): Boolean {
        val darkness = 1 - (0.299 * Color.red(color) + 0.587 * Color.green(color) + 0.114 * Color.blue(color)) / 255
        return darkness < 0.5
    }

    /**
     * Sets up edge-to-edge display with toolbar color detection.
     *
     * @param activity The activity to configure
     * @param toolbar The toolbar view to detect color from
     */
    fun setupEdgeToEdgeWithToolbar(activity: Activity, toolbar: View) {
        // Get toolbar background color
        val toolbarColor = when {
            toolbar.background != null -> {
                try {
                    val drawable = toolbar.background
                    if (drawable is android.graphics.drawable.ColorDrawable) {
                        drawable.color
                    } else {
                        null // Use default
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Failed to get toolbar color")
                    null // Use default
                }
            }
            else -> null // Use default
        }

        // Determine if toolbar is light and set status bar appearance accordingly
        val isLightToolbar = if (toolbarColor != null) {
            isColorLight(toolbarColor)
        } else {
            false // Default to dark toolbar
        }

        // Set status bar color based on toolbar color
        val statusBarColor = if (isLightToolbar) {
            Color.WHITE // Light toolbar gets white status bar
        } else {
            ContextCompat.getColor(activity, R.color.color_primary) // Dark toolbar gets color_primary
        }

        // Set system bar colors
        activity.window.statusBarColor = statusBarColor

        // Configure system bars appearance
        val windowInsetsController = WindowInsetsControllerCompat(activity.window, activity.window.decorView)
        windowInsetsController.isAppearanceLightStatusBars = isLightToolbar

        // Setup edge-to-edge
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
        activity.window.navigationBarColor = Color.TRANSPARENT
        windowInsetsController.isAppearanceLightNavigationBars = false

        // Special handling for Android 15+
        if(OSUtil().isAndroid15OrHigher()) {
            windowInsetsController.systemBarsBehavior =
                WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }

        // Apply insets to toolbar
        applyToolbarInsets(toolbar)
    }

    /**
     * Ensures toolbar isn't drawn under status bar with color detection.
     *
     * @param activity Host activity
     * @param toolbar Target toolbar view
     */
    fun fixToolbarInsets(activity: Activity, toolbar: View) {
        // Get toolbar background color
        val toolbarColor = when {
            toolbar.background != null -> {
                try {
                    val drawable = toolbar.background
                    if (drawable is android.graphics.drawable.ColorDrawable) {
                        drawable.color
                    } else {
                        null // Use default
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Failed to get toolbar color")
                    null // Use default
                }
            }
            else -> null // Use default
        }

        // Always use color_primary for status bar
        val statusBarColor = ContextCompat.getColor(activity, R.color.color_primary)
        activity.window.statusBarColor = statusBarColor

        // Configure system bars appearance based on toolbar color
        val windowInsetsController = WindowInsetsControllerCompat(activity.window, activity.window.decorView)
        val isLightStatusBar = if (toolbarColor != null) {
            isColorLight(toolbarColor)
        } else {
            false // Default to dark toolbar (light icons)
        }

        windowInsetsController.isAppearanceLightStatusBars = isLightStatusBar

        // Apply insets to toolbar
        ViewCompat.setOnApplyWindowInsetsListener(toolbar) { v, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            v.setPadding(
                v.paddingLeft,
                insets.top,
                v.paddingRight,
                v.paddingBottom
            )

            windowInsets
        }

        ViewCompat.requestApplyInsets(toolbar)
    }

    /**
     * Prevents toolbar from being drawn under the status bar.
     *
     * @param toolbar Target toolbar view
     */
    fun applyToolbarInsets(toolbar: View) {
        ViewCompat.setOnApplyWindowInsetsListener(toolbar) { v, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            // For Android 15+, we prefer to use padding instead of margin for better visual results
            if(OSUtil().isAndroid15OrHigher()) { // Android 15 (VANILLA_ICE_CREAM)
                // Apply top padding to the toolbar to account for the status bar
                v.setPadding(
                    v.paddingLeft,
                    insets.top,
                    v.paddingRight,
                    v.paddingBottom
                )
            } else {
                // For older versions, we can use margin or padding
                // Update the toolbar's top margin to account for the status bar
                v.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                    topMargin = insets.top
                }
            }

            // Return the insets so that they are available to other views
            windowInsets
        }

        // Request insets be applied
        ViewCompat.requestApplyInsets(toolbar)
    }



    /**
     * Prevents view from being drawn under the navigation bar.
     *
     * Ideal for bottom-aligned content, buttons, or navigation elements.
     *
     * @param view Target view
     * @param usePadding Use padding instead of margin
     * @param adjustmentFactor Factor to adjust bottom inset (0.8f = 20% reduction)
     */
    fun applyBottomInsets(view: View, usePadding: Boolean = true, adjustmentFactor: Float = 0.8f) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            // Apply adjustment to prevent excessive padding
            val adjustedBottomInset = (insets.bottom * adjustmentFactor).toInt()

            if(usePadding || OSUtil().isAndroid15OrHigher()) { // Android 15 (VANILLA_ICE_CREAM)
                // For Android 15+ or when explicitly requested, use padding for better visual results
                // Preserve existing left, top, and right padding
                v.setPadding(
                    v.paddingLeft,
                    v.paddingTop,
                    v.paddingRight,
                    adjustedBottomInset // Apply adjusted bottom inset as padding
                )
            } else {
                // For older versions, use margin when explicitly requested
                v.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                    bottomMargin = adjustedBottomInset
                }
            }

            // Return the insets so that they are available to other views
            windowInsets
        }

        // Request insets be applied
        ViewCompat.requestApplyInsets(view)
    }

    /**
     * Sets up edge-to-edge display for Android 15+ with consistent status bar color.
     *
     * @param activity Target activity
     */
    fun setupEdgeToEdgeForAndroid15Plus(activity: Activity) {
        // Use the main setupEdgeToEdge method for consistency
        setupEdgeToEdge(activity)
    }

    /**
     * Prevents gaps between content and system bars in edge-to-edge mode.
     *
     * @param view Content container view
     * @param applyTopPadding Apply top padding
     * @param applyBottomPadding Apply bottom padding
     * @param adjustmentFactor Factor to adjust bottom inset (0.8f = 20% reduction)
     */
    fun applyInsetsToContentContainer(
        view: View,
        applyTopPadding: Boolean = false,
        applyBottomPadding: Boolean = true,
        adjustmentFactor: Float = 0.8f
    ) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            // Only apply bottom inset if explicitly requested
            // This helps prevent double-padding issues
            val bottomPadding = if(applyBottomPadding) {
                // Apply adjustment to prevent excessive padding
                (insets.bottom * adjustmentFactor).toInt()
            } else {
                v.paddingBottom // Keep existing padding
            }

            // Apply padding to ensure no gaps with system bars
            v.setPadding(
                insets.left,
                if(applyTopPadding) insets.top else v.paddingTop,
                insets.right,
                bottomPadding
            )

            // Don't consume the insets so they can be passed to child views
            windowInsets
        }

        // Request insets be applied
        ViewCompat.requestApplyInsets(view)
    }

    /**
     * Ensures content isn't hidden by keyboard in edge-to-edge mode.
     *
     * @param rootView Root view of the activity
     * @param footerView Optional footer view to adjust when keyboard appears
     * @param contentView Optional scrollable content view to adjust
     */
    fun handleKeyboardInsets(
        rootView: View,
        footerView: View? = null,
        contentView: View? = null
    ) {
        ViewCompat.setOnApplyWindowInsetsListener(rootView) { _, windowInsets ->
            // Get system bars insets (status bar, navigation bar)
            val systemInsets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            // Get IME (keyboard) insets
            val imeInsets = windowInsets.getInsets(WindowInsetsCompat.Type.ime())

            // Check if keyboard is visible
            val isKeyboardVisible = windowInsets.isVisible(WindowInsetsCompat.Type.ime())

            // Apply left/right insets to the root view
            rootView.setPadding(
                systemInsets.left,
                rootView.paddingTop, // Don't change top padding here
                systemInsets.right,
                0 // Don't apply bottom padding to root - we'll handle it separately
            )

            // Handle footer view (buttons at bottom of screen)
            footerView?.let { footer ->
                // For the footer, we need to handle it differently when the keyboard is visible
                if(isKeyboardVisible) {
                    // When keyboard is visible, we want the footer to appear just above the keyboard
                    // with minimal or no padding to match the original behavior

                    // Find the parent of the footer (usually a FrameLayout or similar)
                    val parent = footer.parent as? ViewGroup

                    // If we can find the parent, adjust its bottom margin to position it above the keyboard
                    parent?.let { p ->
                        p.setPadding(
                            p.paddingLeft,
                            p.paddingTop,
                            p.paddingRight,
                            0 // No bottom padding when keyboard is visible
                        )
                    }

                    // Set the footer's padding to zero or a minimal value
                    footer.setPadding(
                        footer.paddingLeft,
                        footer.paddingTop,
                        footer.paddingRight,
                        0 // No bottom padding when keyboard is visible
                    )
                } else {
                    // When keyboard is not visible, use the navigation bar insets
                    // to ensure the footer isn't drawn under the navigation bar
                    // Apply adjustment to prevent excessive padding
                    val adjustedBottomInset = (systemInsets.bottom * 0.8f).toInt()

                    // Apply the adjusted bottom inset to the footer
                    // This is the ONLY place where bottom inset should be applied to the footer
                    footer.setPadding(
                        footer.paddingLeft,
                        footer.paddingTop,
                        footer.paddingRight,
                        adjustedBottomInset // Use adjusted bottom inset
                    )
                }
            }

            // Handle content view (scrollable content)
            contentView?.let { content ->
                // For scrollable content, we need to adjust padding and clip area
                // when the keyboard is visible to ensure content isn't hidden
                when(content) {
                    is ScrollView -> {
                        // Ensure content isn't clipped by its padding for better scrolling experience
                        content.clipToPadding = false

                        // Always enable fillViewport to ensure proper layout behavior
                        content.isFillViewport = true

                        // Enable smooth scrolling
                        content.isSmoothScrollingEnabled = true

                        // Ensure scrollbars are visible
                        content.isVerticalScrollBarEnabled = true

                        // When keyboard is visible, ensure proper scrolling behavior
                        if(isKeyboardVisible) {
                            // Request layout to adjust to new constraints
                            content.requestLayout()

                            // Post a delayed action to ensure the view is properly measured
                            content.post {
                                // Scroll to show the focused view if any
                                val focusedView = content.findFocus()
                                focusedView?.let { focused ->
                                    val rect = android.graphics.Rect()
                                    focused.getGlobalVisibleRect(rect)
                                    content.requestChildRectangleOnScreen(focused, rect, false)
                                }
                            }
                        }
                    }

                    is androidx.core.widget.NestedScrollView -> {
                        // Ensure content isn't clipped by its padding for better scrolling experience
                        content.clipToPadding = false

                        // Always ensure the NestedScrollView fills its viewport
                        content.isFillViewport = true

                        // Enable smooth scrolling
                        content.isSmoothScrollingEnabled = true

                        // Make sure scrollbars are visible
                        content.isVerticalScrollBarEnabled = true

                        // When keyboard is visible, ensure proper scrolling behavior
                        if(isKeyboardVisible) {
                            // Request layout to adjust to new constraints
                            content.requestLayout()

                            // Post a delayed action to ensure the view is properly measured
                            content.post {
                                // Scroll to show the focused view if any
                                val focusedView = content.findFocus()
                                focusedView?.let { focused ->
                                    val rect = android.graphics.Rect()
                                    focused.getGlobalVisibleRect(rect)
                                    content.smoothScrollTo(0, rect.top - content.scrollY)
                                }
                            }
                        }
                    }
                }
            }

            // Return the insets so they can be applied to child views if needed
            windowInsets
        }

        // Request insets be applied
        ViewCompat.requestApplyInsets(rootView)
    }

    fun preparePhotoPickerActivityEdgeToEdge() {
        // Only needed on Android 15+
        if(!OSUtil().isAndroid15OrHigher()) return

        // Wait for PhotosPickerActivity UI to open, then apply insets
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                // Find and fix PhotosPickerActivity
                getRunningActivities().forEach { activity ->
                    if(activity.javaClass.name.startsWith("com.styletheory.camera.screens.home.PhotosPickerActivity")) {
                        applyEdgeToEdgeToActivity(activity)
                    }
                }
            } catch(e: Exception) {
                Timber.e(e, "Failed to apply insets to PhotosPickerActivity UI")
            }
        }, 300)
    }

    private fun getRunningActivities(): List<Activity> {
        val activities = mutableListOf<Activity>()

        // Get ActivityThread instance
        val activityThreadClass = Class.forName("android.app.ActivityThread")
        val currentActivityThreadMethod = activityThreadClass.getDeclaredMethod("currentActivityThread")
        currentActivityThreadMethod.isAccessible = true
        val activityThread = currentActivityThreadMethod.invoke(null) ?: return activities

        // Get activities map
        val activitiesField = activityThreadClass.getDeclaredField("mActivities")
        activitiesField.isAccessible = true
        val activitiesMap = activitiesField.get(activityThread) as? Map<*, *> ?: return activities

        // Find all activities
        activitiesMap.values.forEach { record ->
            record?.let {
                try {
                    val activityField = it.javaClass.getDeclaredField("activity")
                    activityField.isAccessible = true
                    (activityField.get(it) as? Activity)?.let { activity ->
                        activities.add(activity)
                    }
                } catch(e: Exception) {
                }
            }
        }

        return activities
    }

    private fun applyEdgeToEdgeToActivity(activity: Activity) {
        try {
            // Make window edge-to-edge with transparent system bars
            WindowCompat.setDecorFitsSystemWindows(activity.window, false)

            // Use consistent status bar appearance based on the default color
            val statusBarColor = ContextCompat.getColor(activity, R.color.color_primary)
            activity.window.statusBarColor = statusBarColor

            // Configure the WindowInsetsController to handle system bars and keyboard
            val controller = WindowCompat.getInsetsController(activity.window, activity.window.decorView)
            controller.isAppearanceLightStatusBars = false
            controller.isAppearanceLightNavigationBars = false // Always use white icons for navigation

            // This ensures the keyboard doesn't overlap with content
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE

            // Apply insets to content
            val contentView = activity.findViewById<View>(android.R.id.content)
            if(contentView is ViewGroup && contentView.childCount > 0) {
                val rootView = contentView.getChildAt(0)
                ViewCompat.setOnApplyWindowInsetsListener(rootView) { view, windowInsets ->
                    val statusBarInsets = windowInsets.getInsets(WindowInsetsCompat.Type.statusBars())
                    val navigationBarInsets = windowInsets.getInsets(WindowInsetsCompat.Type.navigationBars())
                    val imeInsets = windowInsets.getInsets(WindowInsetsCompat.Type.ime())

                    // Use the maximum of navigation bar or keyboard insets for bottom padding
                    // This ensures content is pushed above the keyboard when it's visible
                    val bottomInset = maxOf(navigationBarInsets.bottom, imeInsets.bottom)

                    view.updatePadding(
                        top = statusBarInsets.top,
                        bottom = bottomInset
                    )
                    WindowInsetsCompat.CONSUMED
                }
                ViewCompat.requestApplyInsets(rootView)
            }
        } catch(e: Exception) {
            Timber.e(e, "Failed to apply keyboard insets to Container")
        }
    }
}
